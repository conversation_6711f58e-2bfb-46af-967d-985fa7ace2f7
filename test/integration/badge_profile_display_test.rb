require 'test_helper'

class BadgeProfileDisplayTest < ActionDispatch::IntegrationTest
  def setup
    @talent_user = users(:one)
    @scout_user = users(:two)

    # Create a talent profile for testing
    @talent_profile =
      TalentProfile.create!(
        user: @talent_user,
        headline: 'Expert Ghostwriter',
        about: 'Experienced writer with 5+ years in content creation',
        looking_for: 'Long-term writing partnerships',
        availability_status: 'available',
      )

    # Use existing badge types from fixtures
    @verified_badge = badge_types(:verified)
    @premium_badge = badge_types(:premium)

    # Assign badges to talent user
    BadgeAssignment.create!(
      badge_type: @verified_badge,
      user: @talent_user,
      admin: @scout_user,
      assigned_at: Time.current,
    )

    BadgeAssignment.create!(
      badge_type: @premium_badge,
      user: @talent_user,
      admin: @scout_user,
      assigned_at: Time.current,
    )
  end

  test 'talent profile show page displays badges' do
    sign_in_as @talent_user
    get talent_profile_path

    assert_response :success
    assert_select '.badge', count: 2
    assert_select '[data-controller="badge"]', count: 2
    assert_match 'Verified', response.body
    assert_match 'Premium', response.body
  end

  test 'scout viewing talent profile sees badges' do
    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success
    assert_select '.badge', minimum: 2
    assert_select '[data-controller="badge"]', minimum: 2
    assert_match 'Verified', response.body
    assert_match 'Premium', response.body
  end

  test 'talent cards in search results display badges' do
    sign_in_as @scout_user
    get scout_talent_index_path

    assert_response :success

    # Should show badges in talent cards
    assert_select '.badge', minimum: 1
    assert_select '[data-controller="badge"]', minimum: 1
  end

  test 'user without badges shows no badges message' do
    # Create a user without badges
    user_without_badges =
      User.create!(
        email: '<EMAIL>',
        password: 'password123456',
        first_name: 'No',
        last_name: 'Badges',
        verified: true,
      )

    profile_without_badges =
      TalentProfile.create!(
        user: user_without_badges,
        headline: 'Test headline',
        about: 'Test about',
      )

    sign_in_as @scout_user
    get scout_talent_path(profile_without_badges)

    assert_response :success
    assert_match 'No badges assigned', response.body
  end

  test 'badge display respects priority ordering' do
    # Use existing badge type with higher priority
    @top_priority_badge = badge_types(:ghostwrote_choice)

    BadgeAssignment.create!(
      badge_type: @top_priority_badge,
      user: @talent_user,
      admin: @scout_user,
      assigned_at: Time.current,
    )

    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success

    # Check that badges appear in priority order
    badge_elements = css_select('.badge .badge-name')
    badge_names = badge_elements.map(&:text)

    # Verified should appear first due to priority 0 (lower number = higher priority)
    assert_equal 'Verified', badge_names.first
  end

  test 'legacy achievement badges fallback works' do
    # Remove all badge assignments
    @talent_user.badge_assignments.destroy_all

    # Add legacy achievement badges
    @talent_profile.update!(
      achievement_badges: ['Expert Writer', 'Fast Turnaround'],
    )

    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success
    assert_match 'Legacy Achievement Badges', response.body
    assert_match 'Expert Writer', response.body
    assert_match 'Fast Turnaround', response.body
  end

  test 'shared user_badges component renders correctly' do
    sign_in_as @scout_user
    get scout_talent_path(@talent_profile)

    assert_response :success

    # Check for holographic effect data attributes
    assert_select '[data-badge-holographic-intensity-value]'
    assert_select '[data-badge-rotation-factor-value]'
    assert_select '[data-badge-glow-intensity-value]'
    assert_select '[data-badge-prismatic-effect-value="true"]'
  end

  teardown do
    BadgeType.destroy_all
    BadgeAssignment.destroy_all
  end
end
