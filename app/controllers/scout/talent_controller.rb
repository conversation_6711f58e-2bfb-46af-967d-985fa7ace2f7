require "pagy/extras/array"

module Scout
  class TalentController < Scout::BaseController
    before_action :set_filters, only: :index

    def index
      search_query = params[:query].presence || "*"

      @search = TalentProfile.search(
        search_query,
        aggs: {
          "skills" => {
            limit: 20,
            where: agg_filter_conditions(:skills)
          },
          "ghostwriter_type" => {
            limit: 20,
            where: agg_filter_conditions(:ghostwriter_type)
          },
          "niches" => {
            limit: 20,
            where: agg_filter_conditions(:niches)
          },
          "availability_status" => {
            limit: 20,
            where: agg_filter_conditions(:availability_status)
          },
          "location_preference" => {
            limit: 20,
            where: agg_filter_conditions(:location_preference)
          },
          "pricing_model" => {
            limit: 20,
            where: agg_filter_conditions(:pricing_model)
          },
          "location" => {
            limit: 20,
            where: agg_filter_conditions(:location)
          },
          "is_agency" => {
            limit: 2,
            where: agg_filter_conditions(:is_agency)
          }
        },
        where: search_conditions,
        page: params[:page],
        per_page: 20,
        smart_aggs: true
      )

      # Ensure distinct profiles are fetched with badge data preloaded
      @talent_profiles = @search.results.uniq.includes(user: :active_badge_assignments)

      @aggregations = @search.aggs

      @total_count = @talent_profiles.size
    end

    def show
      @talent_profile = TalentProfile.includes(user: :active_badge_assignments).find(params[:id])
      # Load active badges for the talent user, ordered by priority
      @active_badges = @talent_profile.user.active_badges
    end

    private

    def search_conditions
      conditions = {}

      if params[:bookmarked] == "true"
        conditions[:id] = Current.user.bookmarked_talents.select(:id).distinct.map(&:id)
      end

      if params[:sent_requests] == "true"
        # Get talent profile IDs for users where the current scout has sent chat requests
        talent_user_ids = Current.user.sent_chat_requests.select(:talent_id).distinct.pluck(:talent_id)
        talent_profile_ids = TalentProfile.where(user_id: talent_user_ids).select(:id).pluck(:id)
        conditions[:id] = talent_profile_ids
      elsif params[:not_contacted] == "true"
        # Get talent profile IDs for users where the current scout has NOT sent chat requests
        contacted_talent_user_ids = Current.user.sent_chat_requests.select(:talent_id).distinct.pluck(:talent_id)
        not_contacted_talent_profile_ids = TalentProfile.where.not(user_id: contacted_talent_user_ids).select(:id).pluck(:id)
        conditions[:id] = not_contacted_talent_profile_ids
      end

      if params[:is_agency].present?
        conditions[:is_agency] = params[:is_agency] == "true"
      end

      # Convert conditions to boolean values
      conditions.transform_values! { |v| v == "true" ? true : (v == "false" ? false : v) }

      if params[:skills].present?
        conditions[:skills] = { all: params[:skills].split(",") }
      end

      if params[:ghostwriter_type].present?
        conditions[:ghostwriter_type] = { all: params[:ghostwriter_type].split(",") }
      end

      if params[:niches].present?
        conditions[:niches] = { all: params[:niches].split(",") }
      end

      if params[:availability_status].present?
        conditions[:availability_status] = { all: params[:availability_status].split(",") }
      end

      if params[:location_preference].present?
        conditions[:location_preference] = { all: params[:location_preference].split(",") }
      end

      if params[:pricing_model].present?
        conditions[:pricing_model] = { all: params[:pricing_model].split(",") }
      end

      conditions
    end

    def agg_filter_conditions(excluded_filter)
      conditions = search_conditions.clone
      conditions.delete(excluded_filter)
      conditions
    end

    def filters
      render partial: "filters_content"
    end

    def saved
      render partial: "saved_content"
    end

    def set_filters
      @open_to_work =
        case params[:availability]
        when "Open to Work"
          true
        when "Not Available"
          false
        else
          nil
        end
    end
  end
end
